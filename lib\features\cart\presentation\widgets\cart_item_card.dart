import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../bloc/cart_bloc.dart';
import '../../bloc/cart_state.dart';
import '../../utils/cart_utils.dart';

class CartItemCard extends StatefulWidget {
  final CartItemModel item;
  final Function(int)? onUpdateQuantity;
  final VoidCallback? onRemove;
  final bool isEditable;

  const CartItemCard({
    super.key,
    required this.item,
    this.onUpdateQuantity,
    this.onRemove,
    this.isEditable = true,
  });

  @override
  State<CartItemCard> createState() => _CartItemCardState();
}

class _CartItemCardState extends State<CartItemCard> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: AppColors.neutral100,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.neutral200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 80,
                height: 80,
                child: _buildImage(),
              ),
            ),
            const SizedBox(width: 12),

            // Product details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    widget.item.name ?? '--',
                    style:  TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Unit
                  Text(
                    widget.item.unit ?? '0',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.neutral400,
                    ),
                  ),
                  Visibility(
                    visible: (widget.item.quantity ?? 0) >
                        (widget.item.availableQuantity ?? 0),
                    child: Text(
                      (widget.item.availableQuantity ?? 0) > 0
                          ? 'We only have ${widget.item.availableQuantity} of this item in stock. Please reduce your quantity.'
                          : 'This item is currently out of stock. Please check back later.',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.red600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Price and quantity controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if ((widget.item.discountedPrice) != null &&
                              (widget.item.discountedPrice! > 0)) ...[
                            Text(
                              '₹${(widget.item.discountedPrice)!.toStringAsFixed(2)}',
                              style:  TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.neutral600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '₹${widget.item.price?.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                                color: AppColors.neutral400,
                              ),
                            ),
                          ] else ...[
                            Text(
                              '₹${widget.item.price?.toStringAsFixed(2)}',
                              style:  TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.neutral600,
                              ),
                            ),
                          ],
                        ],
                      ),

                      // Quantity controls
                      _buildQuantityControls(widget.item),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    // Check if the image is a network image or a local asset
    if (widget.item.imageUrl?.startsWith('http') ?? false) {
      // Network image
      return CachedNetworkImage(
        imageUrl: widget.item.imageUrl ?? '',
        fit: BoxFit.cover,
        placeholder: (context, url) => Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.primary,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppColors.neutral200,
          child:  Icon(
            Icons.image_not_supported_outlined,
            color: AppColors.neutral400,
          ),
        ),
      );
    } else {
      // Local asset image
      return Image.asset(
        widget.item.imageUrl ?? '',
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: AppColors.neutral200,
          child:  Icon(
            Icons.image_not_supported_outlined,
            color: AppColors.neutral400,
          ),
        ),
      );
    }
  }

  Widget _buildQuantityControls(CartItemModel item) {
    return Row(
      children: [
        _buildRemoveButton(),
        const SizedBox(width: 8),
        BlocBuilder<CartBloc, CartState>(
          builder: (context, state) {
            String productId = item.productId ?? '';
            final quantity = CartUtils.getItemQuantity(
                productId, item.skuID ?? '', state.cart);
            return _buildQuantitySelector(
                quantity, item.availableQuantity ?? 0);
          },
        ),
      ],
    );
  }

  Widget _buildRemoveButton() {
    if (!widget.isEditable || widget.onRemove == null) {
      return const SizedBox();
    }

    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        widget.onRemove!();
      },
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: AppColors.error.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.delete_outline,
          color: AppColors.error,
          size: 16,
        ),
      ),
    );
  }

  Widget _buildQuantitySelector(num quantity, num maxQuantity) {
    // If not editable, just show the quantity
    if (!widget.isEditable) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppColors.neutral100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Qty: $quantity',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    return Row(
      children: [
        _buildQuantityButton(
          icon: Icons.remove,
          onPressed: (maxQuantity > 0) &&
                  (quantity > 1) &&
                  widget.onUpdateQuantity != null
              ? () {
                  HapticFeedback.lightImpact();
                  widget.onUpdateQuantity!(quantity.toInt() - 1);
                }
              : null,
        ),
        Container(
          width: 36,
          alignment: Alignment.center,
          child: Text(
            '$quantity',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color:
                  maxQuantity > 0 ? AppColors.neutral700 : AppColors.neutral300,
            ),
          ),
        ),
        _buildQuantityButton(
          icon: Icons.add,
          onPressed:
              (quantity < maxQuantity) && (widget.onUpdateQuantity != null)
                  ? () {
                      HapticFeedback.lightImpact();
                      widget.onUpdateQuantity!(quantity.toInt() + 1);
                    }
                  : null,
        ),
      ],
    );
  }

  Widget _buildQuantityButton(
      {required IconData icon, VoidCallback? onPressed}) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 28,
        height: 28,
        alignment: Alignment.center,
        child: Icon(
          icon,
          size: 16,
          color: onPressed != null ? AppColors.primary : AppColors.neutral200,
        ),
      ),
    );
  }
}
