import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:rozana/core/services/remote_config_service.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/color_utils.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/features/home/<USER>/widgets/animated_search_placeholder.dart';
import 'package:rozana/features/home/<USER>/widgets/header_widget.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/presentation/widgets/location_state_handler.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_text.dart';
import '../../../../widgets/viewall_category_title.dart';
import '../../../categories/presentation/widgets/category_skeleton_loader.dart';
import '../../../categories/presentation/widgets/categoy_card.dart';

import '../widgets/custom_search_appbar.dart';
import '../widgets/glossy_container.dart';
import '../widgets/section_banner.dart';
import '../widgets/section_most_bought.dart';
import '../widgets/section_previously_bought.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _wasAuthenticated = false;
  // String? _lastAddressId;
  ScrollController categoryScrollController = ScrollController();

  List<dynamic> themeSettings = [
    {
      "color": "#000000",
      "light_color": "#000000",
      "icon": "assets/new/icons/loyalty.png",
      "background_image": "assets/new/images/mega_savings.png",
      "image_height": 300,
      "icon_primary_color": "#FFFFFF",
      "icon_secondary_color": "#B6B6B6"
    },
    {
      "color": "#F0D8F8",
      "light_color": "#F0D8F8",
      "icon": "assets/new/icons/apparel.png",
      "background_image": "",
      "image_height": 0,
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#6D6D6D"
    },
    {
      "color": "#167019",
      "light_color": "#93EA96",
      "icon": "assets/new/icons/local_cafe.png",
      "background_image": "",
      "image_height": 0,
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#FFFFFF"
    },
    {
      "color": "#871108",
      "light_color": "#EC867F",
      "icon": "assets/new/icons/add.png",
      "background_image": "",
      "image_height": 0,
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#FFFFFF"
    }
  ];

  @override
  void initState() {
    super.initState();
    // Initialize authentication state
    themeSettings =
        (RemoteConfigService().getThemeConfig['dashboard']?.isNotEmpty ?? false)
            ? RemoteConfigService().getThemeConfig['dashboard']
            : themeSettings;

    final appState = context.read<AppBloc>().state;

    _wasAuthenticated = appState.maybeMap(
      loaded: (loaded) => loaded.isAuthenticated,
      orElse: () => false,
    );
  }

  @override
  void dispose() {
    categoryScrollController.dispose();
    super.dispose();
  }

  static Color iconPrimaryColor = AppColors.neutral100;
  static Color iconSecondaryColor = AppColors.neutral300;

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to AppBloc changes (authentication)
        BlocListener<AppBloc, AppState>(
          listener: (context, state) {
            state.maybeMap(
              loaded: (loaded) {
                // Check if user just became authenticated
                if (loaded.isAuthenticated && !_wasAuthenticated) {
                  // User just logged in, reload location to show default address
                  context.read<LocationBloc>().reloadDefaultAddress();
                }
                _wasAuthenticated = loaded.isAuthenticated;
              },
              orElse: () {},
            );
          },
        ),
      ],
      child: Scaffold(
          backgroundColor: AppColors.neutral100,
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              BlocBuilder<LocationBloc, LocationState>(
                builder: (context, locationState) {
                  bool isSearviceable = locationState.maybeMap(
                    orElse: () => true,
                    notServiceable: (value) => false,
                  );
                  return BlocBuilder<HomeBloc, HomeState>(
                    buildWhen: (previous, current) =>
                        (previous.scrollOffset != current.scrollOffset) ||
                        (previous.selectedIndex != current.selectedIndex),
                    builder: (context, state) {
                      int selectedIndex = state.selectedIndex;

                      int imageHeight = int.tryParse(
                              (themeSettings[selectedIndex]['image_height']
                                      ?.toString()) ??
                                  '0') ??
                          0;
                      bool isScrolled = state.scrollOffset > (imageHeight);
                      String imageUrl =
                          themeSettings[selectedIndex]['background_image'];
                      return Column(
                        children: [
                          Stack(
                            children: [
                              Container(
                                height:
                                    MediaQuery.of(context).size.height - 100,
                                decoration: BoxDecoration(
                                  color: isScrolled
                                      ? ColorUtils.hexToColor(
                                          themeSettings[selectedIndex]['color'])
                                      : null,
                                  gradient: !isScrolled
                                      ? RadialGradient(
                                          colors: [
                                            ColorUtils.hexToColor(
                                                    themeSettings[selectedIndex]
                                                        ['light_color']) ??
                                                AppColors.primary100,
                                            ColorUtils.hexToColor(
                                                    themeSettings[selectedIndex]
                                                        ['color']) ??
                                                AppColors.primary,
                                          ],
                                          center: Alignment.topCenter,
                                          radius: 1,
                                        )
                                      : null,
                                ),
                              ),
                              Visibility(
                                visible: (isSearviceable &&
                                    (themeSettings[selectedIndex]
                                                ['background_image'] ??
                                            '')
                                        .toString()
                                        .isNotEmpty),
                                child: SizedBox(
                                  height: imageHeight + 500,
                                  width: MediaQuery.of(context).size.width,
                                  child: Center(
                                    child: !isScrolled
                                        ? imageUrl.endsWith('.json')
                                            ? Lottie.asset(imageUrl)
                                            : CustomImage(imageUrl: imageUrl)
                                        : null,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Expanded(child: ColoredBox(color: AppColors.white))
                        ],
                      );
                    },
                  );
                },
              ),
              LayoutBuilder(builder: (context, constraints) {
                return SafeArea(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      getIt<HomeBloc>().add(const HomeEvent.loadHomeData());
                    },
                    edgeOffset: 200,
                    child: CustomScrollView(
                      controller: HomeBloc.scrollController,
                      slivers: [
                        // Top app bar with categories
                        BlocBuilder<HomeBloc, HomeState>(
                          buildWhen: (previous, current) =>
                              previous.selectedIndex != current.selectedIndex,
                          builder: (context, state) {
                            return SliverAppBar(
                              titleSpacing: 0,
                              collapsedHeight: 60,
                              toolbarHeight: 60,
                              expandedHeight: 60,
                              floating: false,
                              pinned: false,
                              stretch: false,
                              snap: false,
                              backgroundColor: Colors.transparent,
                              title: CustomSliverAppBarContent(
                                iconPrimaryColor: ColorUtils.hexToColor(
                                        themeSettings[state.selectedIndex]
                                            ['icon_primary_color']) ??
                                    iconPrimaryColor,
                                iconSecondaryColor: ColorUtils.hexToColor(
                                        themeSettings[state.selectedIndex]
                                            ['icon_secondary_color']) ??
                                    iconSecondaryColor,
                              ),
                            );
                          },
                        ),

                        BlocConsumer<LocationBloc, LocationState>(
                          buildWhen: (previous, current) {
                            return current.maybeMap(
                                loading: (value) => false, orElse: () => true);
                          },
                          listener: (context, state) {
                            state.maybeMap(
                                loaded: (_) async {
                                  getIt<HomeBloc>()
                                      .add(const HomeEvent.loadHomeData());
                                  getIt<HomeBloc>().add(
                                      HomeEvent.switchCategory(
                                          CategoryEntity(
                                              id: 'My Deals',
                                              name: 'My Deals',
                                              collectionId: 'My Deals'),
                                          0));
                                },
                                orElse: () {});
                          },
                          builder: (context, state) {
                            return state.maybeWhen(
                              notServiceable: (_) =>
                                  SliverToBoxAdapter(child: SizedBox()),
                              orElse: () => BlocBuilder<HomeBloc, HomeState>(
                                buildWhen: (previous, current) =>
                                    (previous.scrollOffset !=
                                        current.scrollOffset) ||
                                    (previous.selectedIndex !=
                                        current.selectedIndex),
                                builder: (context, state) {
                                  int selectedIndex = state.selectedIndex;
                                  bool isScrolled = state.scrollOffset >
                                      (themeSettings[selectedIndex]
                                              ['image_height'] ??
                                          0);

                                  return SliverPersistentHeader(
                                    pinned: true,
                                    delegate: _SliverAppBarDelegate(
                                      minHeight: (constraints.maxWidth /
                                          (constraints.maxWidth * 0.0076)),
                                      maxHeight: (constraints.maxWidth /
                                          (constraints.maxWidth * 0.0076)),
                                      child: Container(
                                        decoration: BoxDecoration(
                                            color: isScrolled
                                                ? (ColorUtils.hexToColor(
                                                        themeSettings[
                                                                selectedIndex]
                                                            ['color']) ??
                                                    AppColors.primary)
                                                : Colors.transparent,
                                            border: Border(
                                              bottom: BorderSide(
                                                color: AppColors.neutral600,
                                              ),
                                            )),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // SEARCH BAR
                                            GestureDetector(
                                              onTap: () {
                                                context.push(
                                                    '${RouteNames.search}?initialQuery=');
                                              },
                                              child: Container(
                                                margin: EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: AppDimensions
                                                        .screenHzPadding),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 6,
                                                        vertical: 6),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Row(
                                                  children: [
                                                    SizedBox.square(
                                                      dimension: 36,
                                                      child: Center(
                                                        child: Image.asset(
                                                          'assets/new/icons/search.png',
                                                          width: 24,
                                                          height: 24,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 10),
                                                    Expanded(
                                                      child: SizedBox(
                                                        height: 36,
                                                        child: Row(
                                                          children: [
                                                            CustomText(
                                                              'Search ',
                                                              color: AppColors
                                                                  .primary300,
                                                              fontSize: 16,
                                                              maxLines: 1,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              textHeight: 0.9,
                                                            ),
                                                            AnimatedSearchPlaceholder(),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 24,
                                                      child: VerticalDivider(
                                                        thickness: 1,
                                                        width: 20,
                                                        color: AppColors
                                                            .neutral300,
                                                      ),
                                                    ),
                                                    SizedBox.square(
                                                      dimension: 36,
                                                      child: Center(
                                                        child: Image.asset(
                                                          'assets/new/icons/mic_none.png',
                                                          width: 24,
                                                          height: 24,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Container(
                                              color: isScrolled
                                                  ? (ColorUtils.hexToColor(
                                                          themeSettings[
                                                                  selectedIndex]
                                                              ['color']) ??
                                                      AppColors.primary)
                                                  : Colors.transparent,
                                              child: BlocBuilder<HomeBloc,
                                                  HomeState>(
                                                buildWhen: (previous, current) {
                                                  List<CategoryEntity>
                                                      prevCategories =
                                                      previous.mapOrNull(
                                                              loaded: (value) =>
                                                                  value
                                                                      .categorySections) ??
                                                          [];

                                                  List<CategoryEntity>
                                                      currCategories =
                                                      current.mapOrNull(
                                                              loaded: (value) =>
                                                                  value
                                                                      .categorySections) ??
                                                          [];

                                                  return (prevCategories !=
                                                          currCategories) ||
                                                      (previous
                                                              .selectedCategory !=
                                                          current
                                                              .selectedCategory);
                                                },
                                                builder: (context, state) {
                                                  if (state is HomeInitial ||
                                                      (state is HomeLoaded &&
                                                          state.categorySections ==
                                                              null)) {
                                                    return SingleChildScrollView(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: AppDimensions
                                                              .screenHzPadding),
                                                      scrollDirection:
                                                          Axis.horizontal,
                                                      child: Row(
                                                        children: List.generate(
                                                          10,
                                                          (i) => SizedBox(
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.06,
                                                            width: 60,
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          10),
                                                              child: Column(
                                                                children: [
                                                                  Expanded(
                                                                      child:
                                                                          Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        horizontal:
                                                                            5),
                                                                    child:
                                                                        ShimmerBox(),
                                                                  )),
                                                                  SizedBox(
                                                                      height:
                                                                          4),
                                                                  ShimmerText(
                                                                      height:
                                                                          12)
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  }

                                                  List<CategoryEntity>?
                                                      fetchedCategories =
                                                      state.mapOrNull(
                                                          loaded: (value) => value
                                                              .categorySections);

                                                  List<CategoryEntity>?
                                                      categories = [
                                                    ...fetchedCategories ?? []
                                                  ];

                                                  return Column(
                                                    children: [
                                                      SizedBox(
                                                          // height: 89.4,
                                                          child:
                                                              SingleChildScrollView(
                                                        scrollDirection:
                                                            Axis.horizontal,
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal:
                                                                    AppDimensions
                                                                        .screenHzPadding),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children:
                                                              List.generate(
                                                                  categories
                                                                      .length,
                                                                  (index) {
                                                            CategoryEntity?
                                                                category =
                                                                categories[
                                                                    index];
                                                            bool isSelected = (category
                                                                        .id ==
                                                                    state
                                                                        .selectedCategory
                                                                        .id) ||
                                                                (category
                                                                        .name ==
                                                                    state
                                                                        .selectedCategory
                                                                        .name);
                                                            return Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          20),
                                                              child:
                                                                  AppBarCategoryBox(
                                                                imagePath: index ==
                                                                        0
                                                                    ? 'assets/new/icons/loyalty.png'
                                                                    : category
                                                                            .imageUrl ??
                                                                        ((index >=
                                                                                themeSettings.length)
                                                                            ? ''
                                                                            : themeSettings[index]['icon']),
                                                                isSelected:
                                                                    isSelected,
                                                                categoryName:
                                                                    TextFormatter
                                                                        .getFormattedCategoryText(
                                                                  category.name,
                                                                ),
                                                                iconPrimaryColor:
                                                                    (ColorUtils.hexToColor(themeSettings[selectedIndex]
                                                                            [
                                                                            'icon_primary_color']) ??
                                                                        iconPrimaryColor),
                                                                iconSecondaryColor:
                                                                    (ColorUtils.hexToColor(themeSettings[selectedIndex]
                                                                            [
                                                                            'icon_secondary_color']) ??
                                                                        iconSecondaryColor),
                                                                onTap: () {
                                                                  HapticFeedback
                                                                      .lightImpact();
                                                                  HomeBloc.scrollController?.animateTo(
                                                                      0.0,
                                                                      duration: Duration(
                                                                          milliseconds:
                                                                              200),
                                                                      curve: Curves
                                                                          .easeIn);
                                                                  getIt<HomeBloc>().add(
                                                                      HomeEvent.switchCategory(
                                                                          category,
                                                                          index));
                                                                },
                                                              ),
                                                            );
                                                          }),
                                                        ),
                                                      )),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                        SliverToBoxAdapter(
                          child: BlocBuilder<LocationBloc, LocationState>(
                            builder: (context, locationState) {
                              bool isSearviceable = locationState.maybeMap(
                                orElse: () => true,
                                notServiceable: (value) => false,
                              );
                              return BlocBuilder<HomeBloc, HomeState>(
                                buildWhen: (previous, current) =>
                                    (previous.selectedIndex !=
                                        current.selectedIndex),
                                builder: (context, state) {
                                  int selectedIndex = state.selectedIndex;
                                  int imageHeight = int.tryParse(
                                          (themeSettings[selectedIndex]
                                                      ['image_height']
                                                  ?.toString()) ??
                                              '0') ??
                                      0;

                                  return Container(
                                    height: isSearviceable
                                        ? (imageHeight > 0
                                            ? imageHeight.toDouble()
                                            : 50)
                                        : 0,
                                    color: Colors.transparent,
                                    alignment: Alignment.bottomCenter,
                                    child: CustomPaint(
                                      size: Size(
                                          MediaQuery.of(context).size.width,
                                          30.0),
                                      painter: CurvedTopPainter(
                                        color: selectedIndex == 0
                                            ? AppColors.neutral150
                                            : AppColors.white,
                                      ),
                                      child: SizedBox(
                                        width: MediaQuery.of(context)
                                            .size
                                            .width, // Ensure child matches CustomPaint size if needed
                                        height: 30.0,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),

                        // Main content list
                        SliverList(
                          delegate: SliverChildListDelegate([
                            BlocBuilder<HomeBloc, HomeState>(
                              buildWhen: (previous, current) =>
                                  (previous is! HomeError) ||
                                  (current is! HomeError),
                              builder: (context, state) {
                                return Container(
                                  color: AppColors.white,
                                  child: state.maybeMap(
                                    error: (value) => _buildErrorState(
                                        value.message, context),
                                    orElse: () => LocationStateHandler(
                                      builderHeight:
                                          MediaQuery.of(context).size.height -
                                              100,
                                      child: HomeBody(),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ]),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          )),
    );
  }
}

class HomeBody extends StatelessWidget {
  const HomeBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) =>
          previous.selectedCategory != current.selectedCategory,
      builder: (context, state) {
        CategoryEntity? category = state.selectedCategory;
        return Column(
          children: [
            (category.name.toLowerCase() == 'my deals')
                ? HomeAllData()
                : HomeCategoriesSection(
                    preloadData: true,
                    showTitleBar: false,
                    parentCategory: category,
                    level: 'sub_category',
                  ),
          ],
        );
      },
    );
  }
}

class HomeAllData extends StatelessWidget {
  const HomeAllData({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //Previously Bought
        PreviouslyBoughtSection(),
        SizedBox(height: 20),
        BannerWidget(),
        // Most Bought
        MostBoughtSection(),
        SizedBox(height: 150),
      ],
    );
  }
}

Widget _buildErrorState(String message, BuildContext context) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 50),
        const SizedBox(height: 10),
        Text(message, textAlign: TextAlign.center),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            getIt<HomeBloc>().add(const HomeEvent.loadHomeData());
          },
          child: const Text('Retry'),
        ),
      ],
    ),
  );
}

class CurvedTopPainter extends CustomPainter {
  final Color color;

  CurvedTopPainter({this.color = Colors.green});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill; // Fill the path

    final path = Path();

    // Start from bottom left
    path.moveTo(0, size.height);
    // Draw line to top left
    path.lineTo(0, size.height * 0.8); // Start curve a bit down from top left
    path.cubicTo(
      size.width * 0.25,
      0, // Control point 1 (influences the left side of the curve)
      size.width * 0.75,
      0, // Control point 2 (influences the right side of the curve)
      size.width, size.height * 0.8, // End point of the curve
    );

    // Draw line to bottom right
    path.lineTo(size.width, size.height);
    // Close the path to form a shape
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // Only repaint if properties change
  }
}

class BannerWidget extends StatelessWidget {
  const BannerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocationBloc, LocationState>(
      builder: (context, state) {
        bool isNotServicable = state.mapOrNull(
              notServiceable: (value) => true,
            ) ??
            false;
        return !isNotServicable
            ? BlocBuilder<HomeBloc, HomeState>(
                buildWhen: (previous, current) {
                  if (previous is! HomeLoaded) {
                    return true;
                  }
                  if (current is HomeLoaded) {
                    return previous.banners != current.banners;
                  }
                  return false; // Don’t rebuild for other transitions
                },
                builder: (context, state) {
                  CategoryEntity? selectedCategory = state.selectedCategory;
                  List<BannerEntity>? banners =
                      state.mapOrNull(loaded: (value) => value.banners);
                  return (banners?.isNotEmpty ?? false) &&
                          ((selectedCategory.isAvailable ?? false) ||
                              selectedCategory.name == 'My Deals')
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: 30),
                          child: BannerSection(
                            banners: banners,
                            topPadding: 10,
                            height: 200,
                            showIndicator: false,
                            parentCategory: selectedCategory,
                          ),
                        )
                      : SizedBox();
                },
              )
            : SizedBox();
      },
    );
  }
}

class HomeCategoriesSection extends StatefulWidget {
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
  });

  @override
  State<HomeCategoriesSection> createState() => _HomeCategoriesSectionState();
}

class _HomeCategoriesSectionState extends State<HomeCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  @override
  void didUpdateWidget(covariant HomeCategoriesSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if the parentCategory has changed
    if (widget.parentCategory != oldWidget.parentCategory ||
        widget.level != oldWidget.level) {
      // Also check if level changed if it impacts data loading
      LogMessage.l(
          "Parent category changed from ${oldWidget.parentCategory?.name} to ${widget.parentCategory?.name}");
      _loadCategories(); // Reload categories when parentCategory changes
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
      if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onSubcategorySelected!(_subCategories.first);
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: (widget.parentCategory?.isAvailable ?? false) || _isLoading
          ? _isLoading
              ? SizedBox(
                  height: MediaQuery.of(context).size.height,
                )
              : ConstrainedBox(
                  constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height - 300),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HomeCategorySectionWidget(
                        parentCategory: widget.parentCategory,
                        subCategories: _subCategories,
                      ),
                      BannerWidget(),
                      SizedBox(height: 30),
                      if (widget.parentCategory != null) ...[
                        HeaderWidget(title: "Top Products"),
                        const SizedBox(height: 12),
                        ProductsSection(
                          title: widget.parentCategory?.name ?? 'All Products',
                          showSeeAll: false,
                          category: widget.parentCategory,
                          useGridView: true,
                          shrinkWrap: true,
                          primary: true,
                          physics: NeverScrollableScrollPhysics(),
                          bottomPadding: 100,
                          useCollectionId: true,
                          cardWidth: MediaQuery.of(context).size.width / 2,
                          gridChildAspectRatio: 0.004,
                          onSeeAllTap: () async {},
                        ),
                      ]
                    ],
                  ),
                )
          : Padding(
              padding: const EdgeInsets.only(top: 100),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/curve_background.png'),
                  ),
                  SizedBox(
                    height: 150,
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/coming-soon.png'),
                  ),
                ],
              ),
            ),
    );
  }
}

class HomeSubCategoriesSection extends StatefulWidget {
  final CategoryEntity? mainCategory;
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeSubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
    this.mainCategory,
  });

  @override
  State<HomeSubCategoriesSection> createState() =>
      _HomeSubCategoriesSectionState();
}

class _HomeSubCategoriesSectionState extends State<HomeSubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
              showViewAll: false,
            ),
          ),
        const SizedBox(height: 8),
        _isLoading
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: CategorySkeletonLoader(
                    useGridView: widget.useGridView,
                    showAsRow: widget.showAsRow,
                    gridCrossAxisCount: widget.gridCrossAxisCount,
                    gridChildAspectRatio: 1,
                    itemHeight: 70,
                  ),
                ),
              )
            : GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.screenHzPadding),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: widget.gridCrossAxisCount,
                  childAspectRatio: widget.gridChildAspectRatio,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 12,
                ),
                itemCount: _subCategories.length,
                itemBuilder: (context, index) {
                  CategoryEntity subCategory = _subCategories[index];
                  return CategoryCard(
                    onTap: () async {
                      HapticFeedback.lightImpact();

                      Map<String, dynamic> extras = {};
                      if (widget.parentCategory != null) {
                        extras['category'] = widget.parentCategory;
                        extras['sub_category'] = subCategory;
                        extras['parent-category'] = widget.mainCategory;
                      }
                      context.push(RouteNames.products, extra: extras);
                    },
                    category: widget.parentCategory,
                    subCategory: subCategory,
                    radius: 10,
                    fontSize: 10,
                  );
                },
              ),
      ],
    );
  }
}

class HomeCategorySectionWidget extends StatelessWidget {
  const HomeCategorySectionWidget({
    super.key,
    this.parentCategory,
    this.subCategories = const [],
  });
  final CategoryEntity? parentCategory;
  final List<CategoryEntity> subCategories;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display parent category name as title (like CategoriesSection)
                Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 8, bottom: 15),
                  child: HeaderWidget(title: parentCategory?.name ?? ''),
                ),
                // Display subcategories in grid format (like CategoriesSection)
                GridView.builder(
                  shrinkWrap: true,
                  primary: false,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 10,
                    childAspectRatio: 0.8,
                  ),
                  itemCount: subCategories.length,
                  itemBuilder: (ctx, index) {
                    CategoryEntity subCategory = subCategories[index];
                    return CategoryCard(
                      fontSize: 8,
                      subCategory: subCategory,
                      category: CategoryEntity(
                          id: '',
                          name: '',
                          collectionId: subCategory.parentID ?? ''),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Custom SliverPersistentHeaderDelegate
// This is boilerplate code needed for SliverPersistentHeader
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
