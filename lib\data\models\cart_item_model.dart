import 'package:flutter/foundation.dart';

class CartItemModel {
  String? id;
  String? productId;
  String? name;
  num? price;
  String? imageUrl;
  num? quantity;
  String? unit;
  num? discountedPrice;
  Map<String, dynamic>? customizations;
  String? facilityId;
  String? facilityName;
  String? skuID;
  num? availableQuantity;
  num? maxQuantity;
  num? tax;
  num? cgst;
  num? sgst;
  bool? taxable;

  /// Calculate the total price for this item (quantity * price)
  num get totalPrice => (discountedPrice ?? price ?? 0) * (quantity ?? 0);

  CartItemModel({
    this.id,
    this.productId,
    this.name,
    this.price,
    this.imageUrl,
    this.quantity,
    this.unit,
    this.discountedPrice,
    this.customizations,
    this.facilityId,
    this.facilityName,
    this.skuID,
    this.availableQuantity,
    this.maxQuantity,
    this.tax,
    this.cgst,
    this.sgst,
    this.taxable,
  });

  CartItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    productId = json['productId']?.toString();
    name = json['name']?.toString();
    if (json['price'] is num) {
      price = json['price'];
    } else if (json['price'] != null) {
      var numb = num.tryParse(json['price']!.toString());
      if (numb is num) {
        price = numb;
      }
    }
    imageUrl = json['imageUrl']?.toString();
    if (json['quantity'] is num) {
      quantity = json['quantity'];
    } else if (json['quantity'] != null) {
      var numb = num.tryParse(json['quantity']!.toString());
      if (numb is num) {
        quantity = numb;
      }
    }
    unit = json['unit']?.toString();
    if (json['discountedPrice'] is num) {
      discountedPrice = json['discountedPrice'];
    } else if (json['discountedPrice'] != null) {
      var numb = num.tryParse(json['discountedPrice']!.toString());
      if (numb is num) {
        discountedPrice = numb;
      }
    }
    if (json['customizations'] is Map) {
      customizations = json['customizations'];
    }
    facilityId = json['facilityId']?.toString();
    facilityName = json['facilityName']?.toString();
    skuID = json['sku']?.toString();
    if (json['available_qty'] is num) {
      availableQuantity = json['available_qty'];
    } else if (json['available_qty'] != null) {
      var numb = num.tryParse(json['available_qty']!.toString());
      if (numb is num) {
        availableQuantity = numb;
      }
    }
    if (json['max_purchase_limit'] is num) {
      maxQuantity = json['max_purchase_limit'];
    } else if (json['max_purchase_limit'] != null) {
      var numb = num.tryParse(json['max_purchase_limit']!.toString());
      if (numb is num) {
        maxQuantity = numb;
      }
    }
    if (json['tax'] is num) {
      tax = json['tax'];
    } else if (json['tax'] != null) {
      var numb = num.tryParse(json['tax']!.toString());
      if (numb is num) {
        tax = numb;
      }
    }
    if (json['cgst'] is num) {
      cgst = json['cgst'];
    } else if (json['cgst'] != null) {
      var numb = num.tryParse(json['cgst']!.toString());
      if (numb is num) {
        cgst = numb;
      }
    }
    if (json['sgst'] is num) {
      sgst = json['sgst'];
    } else if (json['sgst'] != null) {
      var numb = num.tryParse(json['sgst']!.toString());
      if (numb is num) {
        sgst = numb;
      }
    }
    taxable = json['taxable'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id is String) {
      data['id'] = id;
    }
    if (productId is String) {
      data['productId'] = productId;
    }
    if (name is String) {
      data['name'] = name;
    }
    if (price is num) {
      data['price'] = price;
    } else if (price != null) {
      var numb = num.tryParse(price.toString());
      if (numb is num) {
        data['price'] = numb;
      }
    }
    if (imageUrl is String) {
      data['imageUrl'] = imageUrl;
    }
    if (quantity is num) {
      data['quantity'] = quantity;
    } else if (quantity != null) {
      var numb = num.tryParse(quantity.toString());
      if (numb is num) {
        data['quantity'] = numb;
      }
    }
    if (unit is String) {
      data['unit'] = unit;
    }
    if (discountedPrice is num) {
      data['discountedPrice'] = discountedPrice;
    } else if (discountedPrice != null) {
      var numb = num.tryParse(discountedPrice.toString());
      if (numb is num) {
        data['discountedPrice'] = numb;
      }
    }
    if (customizations is Map) {
      data['customizations'] = customizations;
    }
    if (facilityId is String) {
      data['facilityId'] = facilityId;
    }
    if (facilityName is String) {
      data['facilityName'] = facilityName;
    }
    if (skuID is String) {
      data['sku'] = skuID;
    }
    if (availableQuantity is num) {
      data['available_qty'] = availableQuantity;
    } else if (availableQuantity != null) {
      var numb = num.tryParse(availableQuantity.toString());
      if (numb is num) {
        data['available_qty'] = numb;
      }
    }
    if (maxQuantity is num) {
      data['max_purchase_limit'] = maxQuantity;
    } else if (maxQuantity != null) {
      var numb = num.tryParse(maxQuantity.toString());
      if (numb is num) {
        data['max_purchase_limit'] = numb;
      }
    }
    if (tax is num) {
      data['tax'] = tax;
    } else if (tax != null) {
      var numb = num.tryParse(tax.toString());
      if (numb is num) {
        data['tax'] = numb;
      }
    }
    if (cgst is num) {
      data['cgst'] = cgst;
    } else if (cgst != null) {
      var numb = num.tryParse(cgst.toString());
      if (numb is num) {
        data['cgst'] = numb;
      }
    }
    if (sgst is num) {
      data['sgst'] = sgst;
    } else if (sgst != null) {
      var numb = num.tryParse(sgst.toString());
      if (numb is num) {
        data['sgst'] = numb;
      }
    }
    data['taxable'] = taxable ?? false;
    return data;
  }

  CartItemModel copyWith({
    String? id,
    String? productId,
    String? name,
    num? price,
    String? imageUrl,
    num? quantity,
    String? unit,
    num? discountedPrice,
    Map<String, dynamic>? customizations,
    String? facilityId,
    String? facilityName,
    String? skuID,
    num? availableQuantity,
    num? maxQuantity,
    num? tax,
    num? cgst,
    num? sgst,
    bool? taxable,
  }) {
    return CartItemModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      discountedPrice: discountedPrice ?? this.discountedPrice,
      customizations: customizations ?? this.customizations,
      facilityId: facilityId ?? this.facilityId,
      facilityName: facilityName ?? this.facilityName,
      skuID: skuID ?? this.skuID,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      tax: tax ?? this.tax,
      cgst: cgst ?? this.cgst,
      sgst: sgst ?? this.sgst,
      taxable: taxable ?? this.taxable,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is CartItemModel &&
        other.id == id &&
        other.productId == productId &&
        other.name == name &&
        other.price == price &&
        other.imageUrl == imageUrl &&
        other.quantity == quantity &&
        other.unit == unit &&
        other.discountedPrice == discountedPrice &&
        mapEquals(other.customizations, customizations) &&
        other.facilityId == facilityId &&
        other.facilityName == facilityName &&
        other.skuID == skuID &&
        other.availableQuantity == availableQuantity &&
        other.maxQuantity == maxQuantity &&
        other.tax == tax &&
        other.cgst == cgst &&
        other.sgst == sgst &&
        other.taxable == taxable;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        name.hashCode ^
        price.hashCode ^
        imageUrl.hashCode ^
        quantity.hashCode ^
        unit.hashCode ^
        discountedPrice.hashCode ^
        customizations.hashCode ^
        facilityId.hashCode ^
        facilityName.hashCode ^
        skuID.hashCode ^
        tax.hashCode ^
        cgst.hashCode ^
        sgst.hashCode ^
        taxable.hashCode;
  }
}
