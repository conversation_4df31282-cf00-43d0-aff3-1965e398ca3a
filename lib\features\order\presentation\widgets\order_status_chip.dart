import 'package:flutter/material.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';

class OrderStatusChip extends StatelessWidget {
  final String status;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;

  const OrderStatusChip({
    super.key,
    required this.status,
    this.fontSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final statusInfo = _getStatusInfo(status);

    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusInfo.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusInfo.borderColor,
          width: 1,
        ),
      ),
      child: CustomText(
        statusInfo.displayText,
        color: statusInfo.textColor,
        fontSize: fontSize ?? 12,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  _StatusInfo _getStatusInfo(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case '10':
        return _StatusInfo(
          displayText: 'Order Placed',
          backgroundColor: AppColors.orderPending.withValues(alpha: 0.1),
          borderColor: AppColors.orderPending.withValues(alpha: 0.3),
          textColor: AppColors.orderPending,
        );
      case 'confirmed':
        return _StatusInfo(
          displayText: 'Confirmed',
          backgroundColor: AppColors.orderConfirmed.withValues(alpha: 0.1),
          borderColor: AppColors.orderConfirmed.withValues(alpha: 0.3),
          textColor: AppColors.orderConfirmed,
        );
      case 'preparing':
        return _StatusInfo(
          displayText: 'Preparing',
          backgroundColor: AppColors.orderPreparing.withValues(alpha: 0.1),
          borderColor: AppColors.orderPreparing.withValues(alpha: 0.3),
          textColor: AppColors.orderPreparing,
        );
      case 'out_for_delivery':
        return _StatusInfo(
          displayText: 'Out for Delivery',
          backgroundColor: AppColors.orderOutForDelivery.withValues(alpha: 0.1),
          borderColor: AppColors.orderOutForDelivery.withValues(alpha: 0.3),
          textColor: AppColors.orderOutForDelivery,
        );
      case 'delivered':
        return _StatusInfo(
          displayText: 'Delivered',
          backgroundColor: AppColors.success.withValues(alpha: 0.1),
          borderColor: AppColors.success.withValues(alpha: 0.3),
          textColor: AppColors.success,
        );
      case 'cancelled':
        return _StatusInfo(
          displayText: 'Cancelled',
          backgroundColor: AppColors.error.withValues(alpha: 0.1),
          borderColor: AppColors.error.withValues(alpha: 0.3),
          textColor: AppColors.error,
        );
      default:
        return _StatusInfo(
          displayText: status.toUpperCase(),
          backgroundColor: AppColors.neutral200.withValues(alpha: 0.1),
          borderColor: AppColors.neutral200.withValues(alpha: 0.3),
          textColor: AppColors.neutral400,
        );
    }
  }
}

class _StatusInfo {
  final String displayText;
  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;

  _StatusInfo({
    required this.displayText,
    required this.backgroundColor,
    required this.borderColor,
    required this.textColor,
  });
}

/// Order status icon widget
class OrderStatusIcon extends StatelessWidget {
  final String status;
  final double size;

  const OrderStatusIcon({
    super.key,
    required this.status,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    final iconData = _getStatusIcon(status);
    final color = _getStatusColor(status);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(
        iconData,
        size: size * 0.6,
        color: color,
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.schedule;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'preparing':
        return Icons.restaurant;
      case 'out_for_delivery':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.info_outline;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppColors.orderPending;
      case 'confirmed':
        return AppColors.orderConfirmed;
      case 'preparing':
        return AppColors.orderPreparing;
      case 'out_for_delivery':
        return AppColors.orderOutForDelivery;
      case 'delivered':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.neutral400;
    }
  }
}
