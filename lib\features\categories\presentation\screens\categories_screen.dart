import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/services/remote_config_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/features/location/presentation/widgets/location_state_handler.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/utils/color_utils.dart';
import '../../bloc/categories_bloc.dart';
import '../widgets/subcategories_section.dart';

// ignore: must_be_immutable
class CategoriesScreen extends StatelessWidget {
  CategoriesScreen({super.key, this.scrollItem});

  final CategoryEntity? scrollItem;

  Map<String, dynamic> themeSettings = {
    "background_color": "#FFFFFF",
    "topbar_color": "#0FFF9E5",
    "icon_primary_color": "#371A56",
    "icon_secondary_color": "#4A2373",
    "highlight_color": {
      "Beverages": "#E7F4FE",
      "Apparel": "#EDF7EE",
      "Personal Care": "#FFF9E5",
    }
  };

  @override
  Widget build(BuildContext context) {
    themeSettings =
        (RemoteConfigService().getThemeConfig['category_screen']?.isNotEmpty ??
                false)
            ? RemoteConfigService().getThemeConfig['category_screen']
            : themeSettings;
    final ItemScrollController itemScrollController = ItemScrollController();
    final ScrollOffsetController scrollOffsetController =
        ScrollOffsetController();
    final ItemPositionsListener itemPositionsListener =
        ItemPositionsListener.create();
    final ScrollOffsetListener scrollOffsetListener =
        ScrollOffsetListener.create();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.neutral100,
        elevation: 0.5,
        title: const Text(
          'Categories',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => context.pop(),
        ),
      ),
      body: LocationStateHandler(
        child: BlocBuilder<CategoriesBloc, CategoriesState>(
          builder: (context, state) {
            return state.map(initial: (_) {
              return const Center(child: CircularProgressIndicator());
            }, loaded: (value) {
              if (scrollItem != null &&
                  (value.categories?.isNotEmpty ?? false)) {
                CategoryEntity item = (value.categories ?? []).firstWhere(
                    (e) => ((e.collectionId == scrollItem?.collectionId) ||
                        (e.name == scrollItem?.name)),
                    orElse: () => value.categories!.first);
                int index = (value.categories ?? []).indexOf(item);
                if (index > 1) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    itemScrollController.scrollTo(
                      index: index,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOutCubic,
                      alignment: 0.06, // Center the item
                    );
                  });
                }
              }

              return RefreshIndicator(
                onRefresh: () async {
                  context
                      .read<CategoriesBloc>()
                      .add(CategoriesEvent.fetchCategories());
                },
                child: (value.categories?.isEmpty ?? false)
                    ? const Center(child: Text('No categories found'))
                    : ScrollablePositionedList.builder(
                        padding: const EdgeInsets.only(bottom: 150),
                        itemCount: value.categories?.length ?? 0,
                        itemBuilder: (context, index) {
                          CategoryEntity? category = value.categories?[index];
                          return CategorySectionWidget(
                            category: category,
                            index: index,
                            themeConfig: themeSettings,
                          );
                        },
                        itemScrollController: itemScrollController,
                        scrollOffsetController: scrollOffsetController,
                        itemPositionsListener: itemPositionsListener,
                        scrollOffsetListener: scrollOffsetListener,
                      ),
              );
            }, error: (value) {
              return Center(
                child: CustomText(value.message),
              );
            });
          },
        ),
      ),
    );
  }
}

class CategorySectionWidget extends StatelessWidget {
  const CategorySectionWidget({
    super.key,
    this.category,
    required this.index,
    required this.themeConfig,
  });
  final CategoryEntity? category;
  final int index;
  final Map<String, dynamic> themeConfig;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.only(top: 12, bottom: 6),
          decoration: BoxDecoration(
            color: ColorUtils.hexToColor(
                themeConfig['highlight_color']?[category?.name] ?? ''),
          ),
          child: SubCategoriesSection(
            preloadData: true,
            parentCategory: category,
            gridChildAspectRatio: 0.62,
            level: 'sub_category',
            onSeeAllTap: () async {
              context.push(RouteNames.products, extra: {
                'category': category,
              });
              // Log category view event to AppsFlyer
              await AppsFlyerEvents.categoryView(
                category?.id ?? '',
                category?.name ?? '',
              );
            },
          ),
        ),
      ],
    );
  }
}
