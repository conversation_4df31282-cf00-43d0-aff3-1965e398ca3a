import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/themes/color_schemes.dart';

class CustomText extends Text {
  const CustomText(
    this.text, {
    this.fontSize,
    this.fontWeight,
    this.color,
    this.textHeight,
    super.key,
    super.style,
    super.strutStyle,
    super.textAlign,
    super.textDirection,
    super.locale,
    super.softWrap,
    super.overflow = TextOverflow.ellipsis,
    super.textScaleFactor,
    super.maxLines,
    super.semanticsLabel,
    super.textWidthBasis,
    super.textHeightBehavior,
    super.selectionColor,
  }) : super(text);

  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final double? textHeight;

  @override
  TextStyle? get style => TextStyle(
        fontSize: (fontSize ?? 14).sp,
        fontWeight: fontWeight,
        color: color ?? AppColors.neutral600,
        height: textHeight,
        
      );
}

class MainHeaderText extends CustomText {
  const MainHeaderText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 24,
    super.fontWeight = FontWeight.w600,
    super.textHeight = 1.4,
    super.textAlign,
    super.overflow = TextOverflow.visible,
    super.maxLines = 2,
  }) : super(headerText);

  final String headerText;
}

class SubHeaderText extends CustomText {
  const SubHeaderText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 14,
    super.fontWeight = FontWeight.w500,
    super.textHeight,
    super.textAlign,
    super.overflow = TextOverflow.ellipsis,
    super.maxLines = 3,
  }) : super(headerText);

  final String headerText;
}

class ButtonText extends CustomText {
  const ButtonText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 14,
    super.fontWeight = FontWeight.w600,
    super.textHeight,
    super.textAlign,
    super.overflow = TextOverflow.clip,
    super.maxLines = 1,
  }) : super(headerText);

  final String headerText;
}
