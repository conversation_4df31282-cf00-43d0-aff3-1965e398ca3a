import 'package:flutter/material.dart';

import '../services/remote_config_service.dart';

class AppColors {
  // A private, non-constant map to hold the fetched colors.
  // This is where our runtime values will be stored.
  static final Map<String, Color?> _colorMap =
      RemoteConfigService().getDynamicColors;

  // A hardcoded color palette to be used as the default.
  // This is a static map to make it easy to set defaults in Remote Config.
  static final Map<String, Color> defaultColors = {
    'primary': const Color(0xFF5C2C90),
    'primary100': const Color(0xFFD8D5EA),
    'primary200': const Color(0xFFBEABD3),
    'primary300': const Color(0xFF9D80BC),
    'primary400': const Color(0xFF7D56A6),
    'primary500': const Color(0xFF5C2C90),
    'primary600': const Color(0xFF4A2373),
    'primary700': const Color(0xFF371A56),
    'primary800': const Color(0xFF25123A),
    'secondary100': const Color(0xFFF9EBEC),
    'secondary200': const Color(0xFFEDC4C5),
    'secondary300': const Color(0xFFE19D9F),
    'secondary400': const Color(0xFFCF6365),
    'secondary500': const Color(0xFF902C2E),
    'secondary600': const Color(0xFF9C3032),
    'secondary700': const Color(0xFF752425),
    'secondary800': const Color(0xFF4E1819),
    'neutral100': const Color(0xFFFFFFFF),
    'neutral150': const Color(0xFFF0F0F6),
    'neutral200': const Color(0xFFDBDBDB),
    'neutral300': const Color(0xFFB6B6B6),
    'neutral400': const Color(0xFF929292),
    'neutral500': const Color(0xFF6D6D6D),
    'neutral600': const Color(0xFF494949),
    'neutral650': const Color(0xFF333333),
    'neutral700': const Color(0xFF242424),
    'neutral800': const Color(0xFF000000),
    'yellow800': const Color(0xFF664D00),
    'yellow700': const Color(0xFF997300),
    'yellow600': const Color(0xFFCC9900),
    'yellow500': const Color(0xFFFFBF00),
    'yellow400': const Color(0xFFFFCC33),
    'yellow300': const Color(0xFFFFDF80),
    'yellow200': const Color(0xFFFFECB2),
    'yellow100': const Color(0xFFFFF9E5),
    'green800': const Color(0xFF1F4721),
    'green700': const Color(0xFF2E6B31),
    'green600': const Color(0xFF3E8E41),
    'green500': const Color(0xFF4DB251),
    'green400': const Color(0xFF71C174),
    'green300': const Color(0xFFA6D8A8),
    'green200': const Color(0xFFCAE8CB),
    'green100': const Color(0xFFEDF7EE),
    'red800': const Color(0xFF610C05),
    'red700': const Color(0xFF911108),
    'red600': const Color(0xFFC1170B),
    'red500': const Color(0xFFF21D0D),
    'red400': const Color(0xFFF44A3E),
    'red300': const Color(0xFFF88E86),
    'red200': const Color(0xFFFBBBB6),
    'red100': const Color(0xFFFEE8E7),
    'blue800': const Color(0xFF053861),
    'blue700': const Color(0xFF085491),
    'blue600': const Color(0xFF0A70C2),
    'blue500': const Color(0xFF0D8DF2),
    'blue400': const Color(0xFF3DA3F5),
    'blue300': const Color(0xFF86C6F8),
    'blue200': const Color(0xFFB6DDFB),
    'blue100': const Color(0xFFE7F4FE),
  };

  // Now, for the magic: static getters that proxy the _colorMap.
  // This provides the clean, constant-like syntax you asked for.
  static Color get primary => _colorMap['primary']!;
  static Color get primary100 => _colorMap['primary100']!;
  static Color get primary200 => _colorMap['primary200']!;
  static Color get primary300 => _colorMap['primary300']!;
  static Color get primary400 => _colorMap['primary400']!;
  static Color get primary500 => _colorMap['primary500']!;
  static Color get primary600 => _colorMap['primary600']!;
  static Color get primary700 => _colorMap['primary700']!;
  static Color get primary800 => _colorMap['primary800']!;

  static Color get secondary100 => _colorMap['secondary100']!;
  static Color get secondary200 => _colorMap['secondary200']!;
  static Color get secondary300 => _colorMap['secondary300']!;
  static Color get secondary400 => _colorMap['secondary400']!;
  static Color get secondary500 => _colorMap['secondary500']!;
  static Color get secondary600 => _colorMap['secondary600']!;
  static Color get secondary700 => _colorMap['secondary700']!;
  static Color get secondary800 => _colorMap['secondary800']!;

  static Color get neutral100 => _colorMap['neutral100']!;
  static Color get neutral150 => _colorMap['neutral150']!;
  static Color get neutral200 => _colorMap['neutral200']!;
  static Color get neutral300 => _colorMap['neutral300']!;
  static Color get neutral400 => _colorMap['neutral400']!;
  static Color get neutral500 => _colorMap['neutral500']!;
  static Color get neutral600 => _colorMap['neutral600']!;
  static Color get neutral650 => _colorMap['neutral650']!;
  static Color get neutral700 => _colorMap['neutral700']!;
  static Color get neutral800 => _colorMap['neutral800']!;

  static Color get yellow800 => _colorMap['yellow800']!;
  static Color get yellow700 => _colorMap['yellow700']!;
  static Color get yellow600 => _colorMap['yellow600']!;
  static Color get yellow500 => _colorMap['yellow500']!;
  static Color get yellow400 => _colorMap['yellow400']!;
  static Color get yellow300 => _colorMap['yellow300']!;
  static Color get yellow200 => _colorMap['yellow200']!;
  static Color get yellow100 => _colorMap['yellow100']!;

  static Color get green800 => _colorMap['green800']!;
  static Color get green700 => _colorMap['green700']!;
  static Color get green600 => _colorMap['green600']!;
  static Color get green500 => _colorMap['green500']!;
  static Color get green400 => _colorMap['green400']!;
  static Color get green300 => _colorMap['green300']!;
  static Color get green200 => _colorMap['green200']!;
  static Color get green100 => _colorMap['green100']!;

  static Color get red800 => _colorMap['red800']!;
  static Color get red700 => _colorMap['red700']!;
  static Color get red600 => _colorMap['red600']!;
  static Color get red500 => _colorMap['red500']!;
  static Color get red400 => _colorMap['red400']!;
  static Color get red300 => _colorMap['red300']!;
  static Color get red200 => _colorMap['red200']!;
  static Color get red100 => _colorMap['red100']!;

  static Color get blue800 => _colorMap['blue800']!;
  static Color get blue700 => _colorMap['blue700']!;
  static Color get blue600 => _colorMap['blue600']!;
  static Color get blue500 => _colorMap['blue500']!;
  static Color get blue400 => _colorMap['blue400']!;
  static Color get blue300 => _colorMap['blue300']!;
  static Color get blue200 => _colorMap['blue200']!;
  static Color get blue100 => _colorMap['blue100']!;

  // Status colors
  static Color success = green500;
  static Color error = red500;
  static Color warning = yellow500;
  static Color info = blue500;
  static const Color white = Color(0xFFFFFFFF);

  // Order status colors
  static Color orderPending = yellow500; // Orange
  static Color orderConfirmed = green500; // Blue
  static Color orderPreparing = yellow700; // Amber
  static Color orderOutForDelivery = primary; // Purple
}
