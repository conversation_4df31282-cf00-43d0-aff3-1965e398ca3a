import 'package:dio/dio.dart';
import '../config/environment_config.dart';
import '../utils/logger.dart';
import 'dio_interceptors.dart';

/// Centralized Google API client following DRY principle
/// Handles all Google API requests (Places, Geocoding, etc.) with consistent configuration
class GoogleApiClient {
  static final GoogleApiClient _instance = GoogleApiClient._internal();
  factory GoogleApiClient() => _instance;
  GoogleApiClient._internal();

  Dio? _dio;

  /// Initialize the Google API client with common configuration
  void _initializeDio() {
    _dio = Dio();
    _dio!.options
      ..connectTimeout = const Duration(seconds: 30)
      ..receiveTimeout = const Duration(seconds: 30)
      ..headers = {
        'Accept': 'application/json',
      };

    // Add logging interceptor for debugging
    _dio!.interceptors.add(
      LoggingInterceptor(tag: 'GoogleAPI'),
    );
  }

  /// Generic method to make Google API requests
  /// Centralizes all Google API communication logic
  Future<GoogleApiResponse<T>> request<T>({
    required String baseUrl,
    required Map<String, dynamic> queryParameters,
    String? tag,
    T Function(Map<String, dynamic>)? parser,
  }) async {
    try {
      // Lazy initialization of Dio
      if (!_isDioInitialized()) {
        _initializeDio();
      }

      // Add API key to query parameters if not present
      if (!queryParameters.containsKey('key')) {
        queryParameters['key'] = EnvironmentConfig.googleMapsApiKey;
      }

      LogMessage.p('Making request to: $baseUrl with params: $queryParameters',
          subTag: tag ?? 'GoogleApiClient');

      final response = await _dio!.get(
        baseUrl,
        queryParameters: queryParameters,
      );

      LogMessage.p('Response status: ${response.statusCode}',
          subTag: tag ?? 'GoogleApiClient');

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;

        // Parse data if parser is provided
        final parsedData = parser != null ? parser(data) : data as T;

        return GoogleApiResponse.success(
          data: parsedData,
          rawResponse: response,
        );
      } else {
        return GoogleApiResponse.error(
          message: 'HTTP ${response.statusCode}: ${response.statusMessage}',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      LogMessage.p('Google API Error: $e', subTag: tag ?? 'GoogleApiClient');
      return GoogleApiResponse.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  Future<String> getDuration({
    required double originLat,
    required double originLng,
    required double destLat,
    required double destLng,
  }) async {
    if (!_isDioInitialized()) {
      _initializeDio();
    }

    final url = 'https://maps.googleapis.com/maps/api/directions/json';
    final params = {
      'origin': '$originLat,$originLng',
      'destination': '$destLat,$destLng',
      'mode': 'driving',
      'departure_time': 'now',
      'key': EnvironmentConfig.googleMapsApiKey,
    };

    try {
      final response = await _dio!.get(url, queryParameters: params);

      if (response.statusCode == 200) {
        final data = response.data;
        final duration = data['routes'][0]['legs'][0]['duration']['text'];
        final trafficDuration =
            data['routes'][0]['legs'][0]['duration_in_traffic']['text'];

        LogMessage.p('Normal Duration: $duration');
        LogMessage.p('Duration with Traffic: $trafficDuration');
        return trafficDuration ?? duration;
      } else {
        LogMessage.p('Failed to get directions: ${response.statusMessage}');
        return "";
      }
    } catch (e) {
      LogMessage.p('Error: $e');
      return "";
    }
  }

  /// Specialized method for Places Autocomplete
  Future<GoogleApiResponse<List<dynamic>>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
  }) async {
    return request<List<dynamic>>(
      baseUrl: EnvironmentConfig.googleMapsBaseUrl,
      queryParameters: {
        'input': input,
        if (sessionToken != null) 'sessiontoken': sessionToken,
      },
      tag: 'PlaceAutocomplete',
      parser: (data) => data['predictions'] as List<dynamic>,
    );
  }

  /// Specialized method for Nearby Search
  Future<GoogleApiResponse<List<dynamic>>> getNearbyPlaces({
    required double latitude,
    required double longitude,
    int radius = 10,
  }) async {
    return request<List<dynamic>>(
      baseUrl: EnvironmentConfig.nearBySearchBaseUrl,
      queryParameters: {
        'location': '$latitude,$longitude',
        'radius': radius.toString(),
      },
      tag: 'NearbySearch',
      parser: (data) => data['results'] as List<dynamic>,
    );
  }

  /// Specialized method for Place Details
  Future<GoogleApiResponse<Map<String, dynamic>>> getPlaceDetails({
    required String placeId,
    String fields = 'name,formatted_address,types',
  }) async {
    return request<Map<String, dynamic>>(
      baseUrl: EnvironmentConfig.placeDetailsBaseUrl,
      queryParameters: {
        'place_id': placeId,
        'fields': fields,
      },
      tag: 'PlaceDetails',
      parser: (data) => data['result'] as Map<String, dynamic>,
    );
  }

  /// Specialized method for Geocoding
  Future<GoogleApiResponse<List<dynamic>>> getGeocodingResults({
    required double latitude,
    required double longitude,
    String resultType = 'street_address|route|intersection',
  }) async {
    return request<List<dynamic>>(
      baseUrl: 'https://maps.googleapis.com/maps/api/geocode/json',
      queryParameters: {
        'latlng': '$latitude,$longitude',
        'result_type': resultType,
      },
      tag: 'Geocoding',
      parser: (data) => data['results'] as List<dynamic>,
    );
  }

  Future<String?> getAddressFromLatLngForWeb(double lat, double lng) async {
    // Ensure Dio is initialized
    if (!_isDioInitialized()) {
      _initializeDio();
    }
    final apiKey = EnvironmentConfig.googleMapsApiKey;
    final url = 'https://maps.googleapis.com/maps/api/geocode/json';
    try {
      final response = await _dio!.get(url, queryParameters: {
        'latlng': '$lat,$lng',
        'key': apiKey,
      });
      if (response.data['status'] == 'OK') {
        final results = response.data['results'];
        if (results.isNotEmpty) {
          final address = results[0]['formatted_address'];
          return address;
        }
      } else {
        LogMessage.p('Geocoding error: ${response.data['status']}');
      }
    } catch (e) {
      LogMessage.p('Dio error: $e');
    }
    return null;
  }

  /// Specialized method for getting placemark data from coordinates for web
  /// Returns address components in a format similar to Placemark for consistency
  Future<List<dynamic>?> getPlacemarkFromCoordinatesForWeb({
    required double latitude,
    required double longitude,
  }) async {
    // Ensure Dio is initialized
    if (!_isDioInitialized()) {
      _initializeDio();
    }
    final apiKey = EnvironmentConfig.googleMapsApiKey;
    final url = 'https://maps.googleapis.com/maps/api/geocode/json';
    try {
      final response = await _dio!.get(url, queryParameters: {
        'latlng': '$latitude,$longitude',
        'key': apiKey,
      });
      if (response.data['status'] == 'OK') {
        final results = response.data['results'];
        if (results.isNotEmpty) {
          // Parse address components to mimic placemark structure
          final components = results[0]['address_components'] as List<dynamic>;
          String? street, locality, administrativeArea, postalCode;

          for (final comp in components) {
            final types = List<String>.from(comp['types'] ?? []);
            if (types.contains('route')) street = comp['long_name'];
            if (types.contains('locality')) locality = comp['long_name'];
            if (types.contains('administrative_area_level_1'))
              administrativeArea = comp['long_name'];
            if (types.contains('postal_code')) postalCode = comp['long_name'];
          }

          return [
            {
              'street': street ?? '',
              'locality': locality ?? '',
              'administrativeArea': administrativeArea ?? '',
              'postalCode': postalCode ?? '',
            }
          ];
        }
      } else {
        LogMessage.p('Geocoding error: ${response.data['status']}');
      }
    } catch (e) {
      LogMessage.p('Dio error: $e');
    }
    return null;
  }

  bool _isDioInitialized() {
    return _dio != null;
  }

  /// Dispose method for cleanup
  void dispose() {
    try {
      _dio?.close();
    } catch (e) {
      LogMessage.p('Error disposing GoogleApiClient: $e',
          subTag: 'GoogleApiClient');
    }
  }
}

/// Generic response wrapper for Google API calls
/// Provides consistent error handling and success states
class GoogleApiResponse<T> {
  final T? data;
  final bool isSuccess;
  final String? errorMessage;
  final int? statusCode;
  final Exception? exception;
  final Response<dynamic>? rawResponse;

  const GoogleApiResponse._({
    this.data,
    required this.isSuccess,
    this.errorMessage,
    this.statusCode,
    this.exception,
    this.rawResponse,
  });

  factory GoogleApiResponse.success({
    required T data,
    Response<dynamic>? rawResponse,
  }) {
    return GoogleApiResponse._(
      data: data,
      isSuccess: true,
      rawResponse: rawResponse,
    );
  }

  factory GoogleApiResponse.error({
    required String message,
    int? statusCode,
    Exception? exception,
  }) {
    return GoogleApiResponse._(
      isSuccess: false,
      errorMessage: message,
      statusCode: statusCode,
      exception: exception,
    );
  }

  /// Convenience method to handle response with callbacks
  R when<R>({
    required R Function(T data) success,
    required R Function(String message) error,
  }) {
    if (isSuccess && data != null) {
      return success(data as T);
    } else {
      return error(errorMessage ?? 'Unknown error occurred');
    }
  }
}
